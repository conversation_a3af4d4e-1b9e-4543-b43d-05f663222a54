import React from "react";
import ReferenceTooltip from "@/components/knowledge/ReferenceTooltip";
import type { ReferenceChunk } from "@/request/chat";

/**
 * 处理包含特殊标记的文本，将##index$$替换为Tooltip组件
 * @param text 原始文本
 * @param chunks 参考文档数组
 * @returns 处理后的React元素数组
 */
export const processTextWithReferences = (
  text: string, 
  chunks: ReferenceChunk[] = []
): React.ReactNode[] => {
  if (!text || chunks.length === 0) {
    return [text];
  }

  // 匹配##数字$$的正则表达式
  const referenceRegex = /##(\d+)\$\$/g;
  const parts: React.ReactNode[] = [];
  let lastIndex = 0;
  let match;
  let keyCounter = 0;

  while ((match = referenceRegex.exec(text)) !== null) {
    const [fullMatch, indexStr] = match;
    const index = parseInt(indexStr, 10);
    const matchStart = match.index;
    const matchEnd = match.index + fullMatch.length;

    // 添加匹配前的文本
    if (matchStart > lastIndex) {
      const beforeText = text.substring(lastIndex, matchStart);
      if (beforeText) {
        parts.push(
          <span key={`text-${keyCounter++}`}>
            {beforeText}
          </span>
        );
      }
    }

    // 检查索引是否有效
    if (index >= 0 && index < chunks.length) {
      const chunk = chunks[index];
      parts.push(
        <ReferenceTooltip 
          key={`ref-${index}-${keyCounter++}`}
          chunk={chunk} 
          index={index} 
        />
      );
    } else {
      // 如果索引无效，保留原始标记
      parts.push(
        <span key={`invalid-${keyCounter++}`} className="text-red-500">
          {fullMatch}
        </span>
      );
    }

    lastIndex = matchEnd;
  }

  // 添加最后剩余的文本
  if (lastIndex < text.length) {
    const remainingText = text.substring(lastIndex);
    if (remainingText) {
      parts.push(
        <span key={`text-final-${keyCounter++}`}>
          {remainingText}
        </span>
      );
    }
  }

  return parts.length > 0 ? parts : [text];
};

/**
 * 检查文本是否包含参考标记
 * @param text 要检查的文本
 * @returns 是否包含参考标记
 */
export const hasReferenceMarkers = (text: string): boolean => {
  const referenceRegex = /##\d+\$\$/g;
  return referenceRegex.test(text);
};

/**
 * 提取文本中的所有参考索引
 * @param text 要处理的文本
 * @returns 参考索引数组
 */
export const extractReferenceIndices = (text: string): number[] => {
  const referenceRegex = /##(\d+)\$\$/g;
  const indices: number[] = [];
  let match;

  while ((match = referenceRegex.exec(text)) !== null) {
    const index = parseInt(match[1], 10);
    if (!isNaN(index)) {
      indices.push(index);
    }
  }

  return indices;
};
