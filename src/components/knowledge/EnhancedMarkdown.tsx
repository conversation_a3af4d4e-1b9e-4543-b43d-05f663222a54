import React from "react";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import { processTextWithReferences, hasReferenceMarkers } from "@/utils/textProcessor";
import type { ReferenceChunk } from "@/request/chat";

interface EnhancedMarkdownProps {
  content: string;
  chunks?: ReferenceChunk[];
  className?: string;
}

const EnhancedMarkdown = ({ content, chunks = [], className }: EnhancedMarkdownProps) => {
  // 如果没有参考标记，直接使用普通的ReactMarkdown
  if (!hasReferenceMarkers(content)) {
    return (
      <div className={className}>
        <ReactMarkdown remarkPlugins={[remarkGfm]}>
          {content}
        </ReactMarkdown>
      </div>
    );
  }

  // 自定义渲染器来处理文本节点中的参考标记
  const components = {
    // 处理段落中的文本
    p: ({ children, ...props }: any) => {
      const processedChildren = React.Children.map(children, (child) => {
        if (typeof child === 'string') {
          return processTextWithReferences(child, chunks);
        }
        return child;
      });
      
      return <p {...props}>{processedChildren}</p>;
    },
    
    // 处理列表项中的文本
    li: ({ children, ...props }: any) => {
      const processedChildren = React.Children.map(children, (child) => {
        if (typeof child === 'string') {
          return processTextWithReferences(child, chunks);
        }
        return child;
      });
      
      return <li {...props}>{processedChildren}</li>;
    },
    
    // 处理标题中的文本
    h1: ({ children, ...props }: any) => {
      const processedChildren = React.Children.map(children, (child) => {
        if (typeof child === 'string') {
          return processTextWithReferences(child, chunks);
        }
        return child;
      });
      
      return <h1 {...props}>{processedChildren}</h1>;
    },
    
    h2: ({ children, ...props }: any) => {
      const processedChildren = React.Children.map(children, (child) => {
        if (typeof child === 'string') {
          return processTextWithReferences(child, chunks);
        }
        return child;
      });
      
      return <h2 {...props}>{processedChildren}</h2>;
    },
    
    h3: ({ children, ...props }: any) => {
      const processedChildren = React.Children.map(children, (child) => {
        if (typeof child === 'string') {
          return processTextWithReferences(child, chunks);
        }
        return child;
      });
      
      return <h3 {...props}>{processedChildren}</h3>;
    },
    
    h4: ({ children, ...props }: any) => {
      const processedChildren = React.Children.map(children, (child) => {
        if (typeof child === 'string') {
          return processTextWithReferences(child, chunks);
        }
        return child;
      });
      
      return <h4 {...props}>{processedChildren}</h4>;
    },
    
    h5: ({ children, ...props }: any) => {
      const processedChildren = React.Children.map(children, (child) => {
        if (typeof child === 'string') {
          return processTextWithReferences(child, chunks);
        }
        return child;
      });
      
      return <h5 {...props}>{processedChildren}</h5>;
    },
    
    h6: ({ children, ...props }: any) => {
      const processedChildren = React.Children.map(children, (child) => {
        if (typeof child === 'string') {
          return processTextWithReferences(child, chunks);
        }
        return child;
      });
      
      return <h6 {...props}>{processedChildren}</h6>;
    },
    
    // 处理强调文本
    strong: ({ children, ...props }: any) => {
      const processedChildren = React.Children.map(children, (child) => {
        if (typeof child === 'string') {
          return processTextWithReferences(child, chunks);
        }
        return child;
      });
      
      return <strong {...props}>{processedChildren}</strong>;
    },
    
    // 处理斜体文本
    em: ({ children, ...props }: any) => {
      const processedChildren = React.Children.map(children, (child) => {
        if (typeof child === 'string') {
          return processTextWithReferences(child, chunks);
        }
        return child;
      });
      
      return <em {...props}>{processedChildren}</em>;
    },
  };

  return (
    <div className={className}>
      <ReactMarkdown 
        remarkPlugins={[remarkGfm]}
        components={components}
      >
        {content}
      </ReactMarkdown>
    </div>
  );
};

export default EnhancedMarkdown;
