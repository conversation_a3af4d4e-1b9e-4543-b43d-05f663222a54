import { useState } from "react";
import { Card, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Bo<PERSON>, User, RefreshCw } from "lucide-react";
import ReferenceDocuments from "./ReferenceDocuments";
import type { Reference } from "@/request/chat";

// 模拟数据
const mockReferences: Reference[] = [
  {
    chunks: [
      {
        document_name: "产品使用手册.pdf",
        content: "本产品是一款智能语音助手，支持多种语言识别和自然语言处理。主要功能包括：\n1. 语音识别：支持中文、英文等多种语言\n2. 智能对话：基于大语言模型的对话能力\n3. 知识问答：可以回答各种专业问题\n4. 任务执行：支持设置提醒、查询天气等功能",
        score: 0.95,
        metadata: {
          page: 1,
          section: "产品介绍",
          file_type: "pdf"
        }
      },
      {
        document_name: "技术规格说明.docx",
        content: "技术参数：\n- 处理器：ARM Cortex-A78 八核处理器\n- 内存：8GB LPDDR5\n- 存储：256GB UFS 3.1\n- 网络：支持Wi-Fi 6、蓝牙5.2\n- 语音识别准确率：>95%\n- 响应时间：<200ms\n- 支持语言：中文、英文、日文、韩文等20+种语言",
        score: 0.88,
        metadata: {
          page: 3,
          section: "硬件规格",
          file_type: "docx"
        }
      },
      {
        document_name: "常见问题解答.md",
        content: "Q: 如何重置设备？\nA: 长按电源键10秒钟，设备会自动重启并恢复出厂设置。\n\nQ: 设备无法连接网络怎么办？\nA: 请检查以下几点：\n1. 确认Wi-Fi密码正确\n2. 检查路由器是否正常工作\n3. 尝试重启设备\n4. 如问题持续，请联系技术支持",
        score: 0.82,
        metadata: {
          section: "故障排除",
          file_type: "markdown",
          last_updated: "2024-01-15"
        }
      }
    ]
  },
  {
    chunks: [
      {
        document_name: "API开发文档.pdf",
        content: "REST API 接口说明：\n\nPOST /api/v1/chat\n请求参数：\n- message: string (必填) - 用户消息\n- session_id: string (可选) - 会话ID\n- model: string (可选) - 模型名称，默认为gpt-3.5-turbo\n\n响应格式：\n{\n  \"code\": 200,\n  \"data\": {\n    \"answer\": \"AI回复内容\",\n    \"session_id\": \"会话ID\"\n  }\n}",
        score: 0.91
      }
    ]
  },
  {
    // 测试向后兼容性 - 没有chunks的情况
    legacy_data: {
      source: "legacy_system",
      documents: ["doc1.pdf", "doc2.txt"],
      confidence: 0.75
    }
  }
];

const ReferenceDemo = () => {
  const [currentIndex, setCurrentIndex] = useState(0);
  
  const currentReference = mockReferences[currentIndex];
  
  const nextExample = () => {
    setCurrentIndex((prev) => (prev + 1) % mockReferences.length);
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold">参考文档展示优化</h1>
        <p className="text-muted-foreground">
          展示优化后的参考文档组件，支持折叠展开和美观的UI设计
        </p>
      </div>

      <div className="flex justify-center">
        <Button onClick={nextExample} className="flex items-center gap-2">
          <RefreshCw className="h-4 w-4" />
          切换示例 ({currentIndex + 1}/{mockReferences.length})
        </Button>
      </div>

      {/* 模拟聊天界面 */}
      <div className="space-y-4">
        {/* 用户消息 */}
        <div className="flex justify-end">
          <div className="flex gap-3 max-w-[80%] flex-row-reverse">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 rounded-full bg-primary flex items-center justify-center">
                <User className="h-4 w-4 text-primary-foreground" />
              </div>
            </div>
            <Card className="bg-primary text-primary-foreground">
              <CardContent className="p-3">
                <p className="whitespace-pre-wrap">
                  请介绍一下这个产品的主要功能和技术规格
                </p>
                <div className="text-xs opacity-70 mt-2">
                  {new Date().toLocaleTimeString()}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* AI回复 */}
        <div className="flex justify-start">
          <div className="flex gap-3 max-w-[80%]">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 rounded-full bg-muted flex items-center justify-center">
                <Bot className="h-4 w-4 text-muted-foreground" />
              </div>
            </div>
            <Card>
              <CardContent className="p-3">
                <div className="space-y-3">
                  <div className="prose prose-sm max-w-none dark:prose-invert">
                    <p>
                      根据产品文档，这是一款智能语音助手产品，具有以下主要功能：
                    </p>
                    <ul>
                      <li><strong>语音识别</strong>：支持20+种语言，识别准确率超过95%</li>
                      <li><strong>智能对话</strong>：基于大语言模型，响应时间小于200ms</li>
                      <li><strong>知识问答</strong>：可以回答各种专业问题</li>
                      <li><strong>任务执行</strong>：支持设置提醒、查询天气等功能</li>
                    </ul>
                    <p>
                      技术规格方面，采用ARM Cortex-A78八核处理器，配备8GB内存和256GB存储空间，
                      支持Wi-Fi 6和蓝牙5.2连接。
                    </p>
                  </div>
                  
                  {/* 参考文档组件 */}
                  <ReferenceDocuments reference={currentReference} />
                </div>
                
                <div className="text-xs opacity-70 mt-2">
                  {new Date().toLocaleTimeString()}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      {/* 说明文档 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Badge variant="outline">功能说明</Badge>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="grid md:grid-cols-2 gap-4">
            <div>
              <h4 className="font-medium mb-2">✨ 主要优化</h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• 默认只显示文档名称列表</li>
                <li>• 点击展开显示完整内容</li>
                <li>• 支持相关度评分显示</li>
                <li>• 美观的卡片式布局</li>
                <li>• 平滑的展开/收起动画</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium mb-2">🔧 技术特性</h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• 支持chunks数组结构</li>
                <li>• 向后兼容原有JSON格式</li>
                <li>• 响应式设计适配移动端</li>
                <li>• 支持元数据信息展示</li>
                <li>• TypeScript类型安全</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ReferenceDemo;
