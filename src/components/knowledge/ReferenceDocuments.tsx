import { useState } from "react";
import { ChevronDown, ChevronRight, FileText, BookOpen } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { cn } from "@/lib/utils";
import type { Reference, ReferenceChunk } from "@/request/chat";

interface ReferenceDocumentsProps {
  reference: Reference;
}

const ReferenceDocuments = ({ reference }: ReferenceDocumentsProps) => {
  const [expandedItems, setExpandedItems] = useState<Set<number>>(new Set());

  // 检查是否有chunks数组
  const chunks = reference?.chunks || [];
  
  // 如果没有chunks，显示原始JSON格式（向后兼容）
  if (chunks.length === 0) {
    return (
      <div className="border-t pt-3">
        <Badge variant="secondary" className="mb-2">
          <BookOpen className="h-3 w-3 mr-1" />
          参考文档
        </Badge>
        <div className="text-xs text-muted-foreground bg-muted/30 p-2 rounded">
          <pre className="whitespace-pre-wrap font-mono">
            {JSON.stringify(reference, null, 2)}
          </pre>
        </div>
      </div>
    );
  }

  const toggleExpanded = (index: number) => {
    const newExpanded = new Set(expandedItems);
    if (newExpanded.has(index)) {
      newExpanded.delete(index);
    } else {
      newExpanded.add(index);
    }
    setExpandedItems(newExpanded);
  };

  return (
    <div className="border-t pt-3 space-y-3">
      <div className="flex items-center gap-2">
        <Badge variant="secondary" className="flex items-center gap-1">
          <BookOpen className="h-3 w-3" />
          参考文档
        </Badge>
        <span className="text-xs text-muted-foreground">
          共 {chunks.length} 个文档片段
        </span>
      </div>

      <div className="space-y-2">
        {chunks.map((chunk, index) => (
          <ReferenceChunkItem
            key={index}
            chunk={chunk}
            index={index}
            isExpanded={expandedItems.has(index)}
            onToggle={() => toggleExpanded(index)}
          />
        ))}
      </div>
    </div>
  );
};

interface ReferenceChunkItemProps {
  chunk: ReferenceChunk;
  index: number;
  isExpanded: boolean;
  onToggle: () => void;
}

const ReferenceChunkItem = ({ chunk, index, isExpanded, onToggle }: ReferenceChunkItemProps) => {
  return (
    <Card className="overflow-hidden transition-all duration-200 hover:shadow-sm">
      <Collapsible open={isExpanded} onOpenChange={onToggle}>
        <CollapsibleTrigger asChild>
          <Button
            variant="ghost"
            className="w-full justify-between p-3 h-auto text-left hover:bg-muted/50 hover:text-foreground"
          >
            <div className="flex items-center gap-2 min-w-0 flex-1">
              <FileText className="h-4 w-4 text-muted-foreground flex-shrink-0" />
              <span className="text-sm font-medium truncate">
                {chunk.document_name || `文档片段 ${index + 1}`}
              </span>
              {chunk.score && (
                <Badge variant="outline" className="text-xs ml-auto mr-2">
                  相关度: {(chunk.score * 100).toFixed(1)}%
                </Badge>
              )}
            </div>
            <div className="flex-shrink-0">
              {isExpanded ? (
                <ChevronDown className="h-4 w-4 text-muted-foreground" />
              ) : (
                <ChevronRight className="h-4 w-4 text-muted-foreground" />
              )}
            </div>
          </Button>
        </CollapsibleTrigger>
        
        <CollapsibleContent className="data-[state=open]:animate-collapsible-down data-[state=closed]:animate-collapsible-up">
          <CardContent className="pt-0 pb-3 px-3">
            <div className="border-t pt-3">
              <div className="text-xs text-muted-foreground mb-2 font-medium">
                文档内容：
              </div>
              <div className="text-sm bg-muted/30 p-3 rounded-md">
                <div className="whitespace-pre-wrap leading-relaxed">
                  {chunk.content || "暂无内容"}
                </div>
              </div>
              
              {chunk.metadata && Object.keys(chunk.metadata).length > 0 && (
                <div className="mt-3">
                  <div className="text-xs text-muted-foreground mb-2 font-medium">
                    元数据：
                  </div>
                  <div className="text-xs bg-muted/20 p-2 rounded border">
                    <pre className="whitespace-pre-wrap font-mono">
                      {JSON.stringify(chunk.metadata, null, 2)}
                    </pre>
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </CollapsibleContent>
      </Collapsible>
    </Card>
  );
};

export default ReferenceDocuments;
