import React from "react";
import { Info } from "lucide-react";
import {
  <PERSON><PERSON><PERSON>,
  Tooltip<PERSON>ontent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Badge } from "@/components/ui/badge";
import type { ReferenceChunk } from "@/request/chat";

interface ReferenceTooltipProps {
  chunk: ReferenceChunk;
  index: number;
}

const ReferenceTooltip = ({ chunk, index }: ReferenceTooltipProps) => {
  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <span className="inline-flex items-center justify-center w-4 h-4 bg-blue-100 hover:bg-blue-200 text-blue-600 rounded-full cursor-help transition-colors duration-200 mx-0.5">
            <Info className="w-2.5 h-2.5" />
          </span>
        </TooltipTrigger>
        <TooltipContent 
          side="top" 
          className="max-w-sm p-3 bg-popover border shadow-lg"
          sideOffset={5}
        >
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <Badge variant="secondary" className="text-xs">
                参考 #{index + 1}
              </Badge>
              <span className="text-xs font-medium text-muted-foreground">
                {chunk.document_name || `文档片段 ${index + 1}`}
              </span>
            </div>
            <div className="text-sm leading-relaxed max-h-32 overflow-y-auto">
              {chunk.content ? (
                <div className="whitespace-pre-wrap">
                  {chunk.content.length > 200 
                    ? `${chunk.content.substring(0, 200)}...` 
                    : chunk.content
                  }
                </div>
              ) : (
                <span className="text-muted-foreground italic">暂无内容</span>
              )}
            </div>
            {chunk.score && (
              <div className="text-xs text-muted-foreground">
                相关度: {(chunk.score * 100).toFixed(1)}%
              </div>
            )}
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};

export default ReferenceTooltip;
