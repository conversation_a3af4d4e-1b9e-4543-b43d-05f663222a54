import { useState, useEffect, useRef } from "react";
import { Send, Bot, User, Loader2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/components/ui/use-toast";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import EnhancedMarkdown from "./EnhancedMarkdown";
import {
  createChatSession,
  sendChatMessage,
  type ChatRequest,
  type ChatResponse,
  type Reference,
  SessionRequest,
} from "@/request/chat";
import ReferenceDocuments from "./ReferenceDocuments";

interface Message {
  id: string;
  type: "user" | "bot";
  content: string;
  timestamp: Date;
  reference?: Reference;
}

interface KnowledgeChatDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  knowledgeBase: {
    id: string;
    name: string;
  } | null;
}

const KnowledgeChatDialog = ({
  open,
  onOpenChange,
  knowledgeBase,
}: KnowledgeChatDialogProps) => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputValue, setInputValue] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [sessionId, setSessionId] = useState<string>("");
  const [isInitializing, setIsInitializing] = useState(false);
  const { toast } = useToast();
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // 滚动到底部
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  // 初始化会话
  const initializeSession = async () => {
    if (!knowledgeBase?.id) return;

    setIsInitializing(true);
    try {
      const newSessionId = await createChatSession(knowledgeBase.id);
      setSessionId(newSessionId);
      setMessages([]);
    } catch (error) {
      toast({
        title: "会话初始化失败",
        description: "无法创建聊天会话，请重试",
        variant: "destructive",
      });
    } finally {
      setIsInitializing(false);
    }
  };

  // 当对话框打开时初始化会话
  useEffect(() => {
    if (open && knowledgeBase) {
      initializeSession();
    }
  }, [open, knowledgeBase]);

  // 自动滚动到底部
  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // 发送消息
  const handleSendMessage = async () => {
    if (!inputValue.trim() || !sessionId || !knowledgeBase?.id || isLoading) {
      return;
    }

    const userMessage: Message = {
      id: Date.now().toString(),
      type: "user",
      content: inputValue.trim(),
      timestamp: new Date(),
    };

    setMessages((prev) => [...prev, userMessage]);
    setInputValue("");
    setIsLoading(true);

    // 创建一个临时的bot消息用于显示加载状态
    const botMessageId = (Date.now() + 1).toString();
    const tempBotMessage: Message = {
      id: botMessageId,
      type: "bot",
      content: "",
      timestamp: new Date(),
    };

    setMessages((prev) => [...prev, tempBotMessage]);

    const chatRequest: ChatRequest = {
      knowledgeId: knowledgeBase.id,
      question: userMessage.content + "/no_think",
      sessionId: sessionId,
    };

    let accumulatedAnswer = "";
    let finalReference = {};

    try {
      await sendChatMessage(
        chatRequest,
        (data: ChatResponse) => {
          console.log("✅ 成功接收到数据:", data);

          // 检查数据结构并提取answer
          if (data && data.data && data.data.answer !== undefined) {
            // 移除 <think> 标签内容
            const cleanAnswer = data.data.answer.replace(
              /<think>[\s\S]*?<\/think>/g,
              ""
            ).trim();

            console.log("📝 原始答案:", data.data.answer);
            console.log("✨ 清理后的答案:", cleanAnswer);
            console.log("📚 引用信息:", data.data.reference);

            // 更新累积答案
            accumulatedAnswer = cleanAnswer;

            // 保存引用信息
            if (data.data.reference) {
              finalReference = data.data.reference;
            }

            // 更新消息
            setMessages((prev) =>
              prev.map((msg) =>
                msg.id === botMessageId
                  ? {
                      ...msg,
                      content: accumulatedAnswer,
                      reference: finalReference,
                    }
                  : msg
              )
            );
          } else {
            console.warn("❌ 数据格式不正确或缺少answer字段:", data);
          }
        },
        (error: Error) => {
          toast({
            title: "发送消息失败",
            description: error.message,
            variant: "destructive",
          });
          // 移除临时消息
          setMessages((prev) => prev.filter((msg) => msg.id !== botMessageId));
        },
        () => {
          setIsLoading(false);
        }
      );
    } catch (error) {
      toast({
        title: "发送消息失败",
        description: "请检查网络连接后重试",
        variant: "destructive",
      });
      // 移除临时消息
      setMessages((prev) => prev.filter((msg) => msg.id !== botMessageId));
      setIsLoading(false);
    }
  };

  // 处理键盘事件
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[900px] h-[90vh] flex flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Bot className="h-5 w-5" />
            知识库问答 - {knowledgeBase?.name}
          </DialogTitle>
        </DialogHeader>

        {isInitializing ? (
          <div className="flex-1 flex items-center justify-center">
            <div className="flex items-center gap-2 text-muted-foreground">
              <Loader2 className="h-4 w-4 animate-spin" />
              正在初始化会话...
            </div>
          </div>
        ) : (
          <>
            {/* 聊天消息区域 */}
            <ScrollArea className="flex-1 pr-4" ref={scrollAreaRef}>
              <div className="space-y-4">
                {messages.length === 0 ? (
                  <div className="text-center text-muted-foreground py-8">
                    <Bot className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>开始与知识库对话吧！</p>
                    <p className="text-sm mt-2">
                      您可以询问关于"{knowledgeBase?.name}"的任何问题
                    </p>
                  </div>
                ) : (
                  messages.map((message) => (
                    <MessageBubble key={message.id} message={message} />
                  ))
                )}
                <div ref={messagesEndRef} />
              </div>
            </ScrollArea>

            {/* 输入区域 */}
            <div className="border-t pt-4">
              <div className="flex gap-2">
                <Textarea
                  value={inputValue}
                  onChange={(e) => setInputValue(e.target.value)}
                  onKeyDown={handleKeyDown}
                  placeholder="输入您的问题..."
                  className="min-h-[60px] max-h-[120px] resize-none"
                  disabled={isLoading}
                />
                <Button
                  onClick={handleSendMessage}
                  disabled={!inputValue.trim() || isLoading}
                  size="icon"
                  className="h-[60px] w-[60px]"
                >
                  {isLoading ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <Send className="h-4 w-4" />
                  )}
                </Button>
              </div>
            </div>
          </>
        )}
      </DialogContent>
    </Dialog>
  );
};

// 消息气泡组件
const MessageBubble = ({ message }: { message: Message }) => {
  const isUser = message.type === "user";

  return (
    <div className={`flex ${isUser ? "justify-end" : "justify-start"}`}>
      <div className={`flex gap-3 max-w-[80%] ${isUser ? "flex-row-reverse" : ""}`}>
        <div className="flex-shrink-0">
          {isUser ? (
            <div className="w-8 h-8 rounded-full bg-primary flex items-center justify-center">
              <User className="h-4 w-4 text-primary-foreground" />
            </div>
          ) : (
            <div className="w-8 h-8 rounded-full bg-muted flex items-center justify-center">
              <Bot className="h-4 w-4 text-muted-foreground" />
            </div>
          )}
        </div>

        <Card className={`${isUser ? "bg-primary text-primary-foreground" : ""}`}>
          <CardContent className="p-3">
            {isUser ? (
              <p className="whitespace-pre-wrap">{message.content}</p>
            ) : (
              <div className="space-y-3">
                <div className="prose prose-sm max-w-none dark:prose-invert">
                  <ReactMarkdown remarkPlugins={[remarkGfm]}>
                    {message.content || "正在思考..."}
                  </ReactMarkdown>
                </div>
                
                {message.reference && Object.keys(message.reference).length > 0 && (
                  <ReferenceDocuments reference={message.reference} />
                )}
              </div>
            )}
            
            <div className="text-xs opacity-70 mt-2">
              {message.timestamp.toLocaleTimeString()}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default KnowledgeChatDialog;
