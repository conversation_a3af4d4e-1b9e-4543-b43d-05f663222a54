import { motion } from "framer-motion";
import { cn } from "@/lib/utils";
import {
  MoreVertical,
  FileText,
  FileQuestion,
  Pencil,
  Trash,
  Check,
  Plus,
  MessageCircle,
} from "lucide-react";
import { <PERSON>, CardContent, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import dayjs from "dayjs";
import { Knowledge } from "@/request/knowledge";

export interface KnowledgeCardProps {
  id?: string;
  name: string;
  description: string;
  createTime?: string;
  fileCount: number;
  isSelected?: boolean;
  isChoice?: boolean;
  onEdit?: (id: string) => void;
  onDelete?: (id: string) => void;
  onSelect?: (id: string) => void;
  onChange?: (data: Partial<Knowledge>) => void;
  onChat?: (data: Partial<Knowledge>) => void;
}

const KnowledgeCard = ({
  id,
  name,
  description,
  createTime,
  fileCount,
  isChoice = false,
  isSelected = false,
  onEdit,
  onDelete,
  onSelect,
  onChange,
  onChat,
}: KnowledgeCardProps) => {
  const handleEdit = () => {
    if (onEdit) {
      onEdit(id);
    }
  };

  const handleDelete = () => {
    if (onDelete) {
      onDelete(id);
    }
  };
  const handleSelect = () => {
    if (onSelect) {
      onSelect(id);
    }
  };
  const handleContent = () => {
    onChange({ id, name });
  };

  const handleChat = () => {
    if (onChat) {
      onChat({ id, name });
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: 10 }}
      whileHover={{ y: -5 }}
      transition={{ duration: 0.3 }}
    >
      <Card
        className={cn(
          "overflow-hidden transition-all duration-300 h-full flex flex-col",
          "hover:shadow-md",
          "border-opacity-40"
        )}
      >
        <CardContent className="p-6 flex-grow">
          <div className="flex justify-between items-start">
            <Badge variant="default" className="mb-3">
              <div className="flex items-center gap-1">
                <FileText className="h-3 w-3" />
                <span>文档知识库</span>
              </div>
            </Badge>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon" className="h-8 w-8">
                  <MoreVertical className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem
                  onClick={handleEdit}
                  className="cursor-pointer"
                >
                  <Pencil className="h-4 w-4 mr-2" />
                  编辑
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  onClick={handleDelete}
                  className="cursor-pointer text-destructive"
                >
                  <Trash className="h-4 w-4 mr-2" />
                  删除
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          <h3 className="font-semibold text-lg truncate">{name}</h3>
          <p className="text-sm text-muted-foreground mt-1 line-clamp-2">
            {description}
          </p>

          <div className="mt-4 text-sm text-muted-foreground">
            <p className="mt-1">
              文档数量：{fileCount}
            </p>
          </div>
          <div className="mt-4 text-sm text-muted-foreground">
            <p className="mt-1">
              创建时间：
              {createTime
                ? dayjs(createTime).format("YYYY-MM-DD HH:mm:ss")
                : ""}
            </p>
          </div>
        </CardContent>

        <CardFooter className="p-4 pt-0 border-t bg-muted/20">
          {!isChoice && (
            <div className="flex gap-2 w-full">
              <Button
                variant="outline"
                size="sm"
                className="gap-1 flex-1"
                onClick={handleContent}
              >
                <Pencil className="h-4 w-4" />
                知识库管理
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="gap-1 flex-1"
                onClick={handleChat}
              >
                <MessageCircle className="h-4 w-4" />
                知识库问答
              </Button>
            </div>
          )}
          {isChoice && (
            <Button
              variant={isSelected ? "default" : "outline"}
              size="sm"
              className="gap-1 w-full"
              onClick={handleSelect}
            >
              {isSelected ? (
                <>
                  <Check className="h-4 w-4" />
                  已选择
                </>
              ) : (
                <>
                  <Plus className="h-4 w-4" />
                  选择
                </>
              )}
            </Button>
          )}
        </CardFooter>
      </Card>
    </motion.div>
  );
};

export default KnowledgeCard;
