import { motion } from "framer-motion";
import { cn } from "@/lib/utils";
import {
  MoreVertical,
  VideoIcon,
  ImageIcon,
  Music,
  Play,
  FileText,
  FileQuestion,
  Cpu,
  Pencil,
  Trash,
  Eye,
  Power,
  PaintBucket,
} from "lucide-react";
import { <PERSON>, CardContent, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import LoadingDialog from "@/components/ui/LoadingDialog";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { useState } from "react";
import { Switch } from "@/components/ui/switch";
import dayjs from "dayjs";
import { type Project } from "@/request/project";
import { useNavigate } from "react-router-dom";
import { useToast } from "@/components/ui/use-toast";
import { UProject } from "@/request/project";
import { useRequest } from "ahooks";
import type { Response } from "../vite-env";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

export interface ProjectCardProps {
  id?: string;
  audioClipId?: string;
  audioClipName?: string;
  name?: string;
  videoClipId?: string;
  videoClipUrl?: string;
  knowledgeId?: string;
  createTime?: string;
  type?: string;
  status?: string;
  publicUrl?: string;
  knowledgeName?: string;
  onEdit?: (project: Partial<Project>) => void;
  onDelete?: (id: string) => void;
}

const ProjectCard = ({
  id,
  audioClipId = "",
  audioClipName = "",
  name = "",
  videoClipId = "",
  videoClipUrl = "",
  knowledgeId = "",
  knowledgeName = "",
  createTime = "",
  type = "",
  status,
  publicUrl,
  onEdit,
  onDelete,
}: ProjectCardProps) => {
  const { toast } = useToast();
  const [innerStatus, setInnerStatus] = useState<string | null>(status);
  const [innerPublicUrl, setInnerPublicUrl] = useState<string | null>(
    publicUrl
  );
  const [uploading, setUploading] = useState(false);

  const navigate = useNavigate();

  const { run: runUpdate } = useRequest(UProject, {
    manual: true,
    onSuccess: (data: Response<Project>, params) => {
      setUploading(false);
      setInnerPublicUrl(data.data.publicUrl);
      toast({
        title: "操作成功",
        description: `项目${
          params?.[0]?.status === "Running" ? "启动" : "停止"
        }成功`,
      });
    },
    onError: (e, params) => {
      setUploading(false);
      let message;
      if (e?.response?.data?.code == "10008") {
        message = "服务器资源不足，请联系系统管理员处理";
      } else if (e?.response?.data?.code == "10010") {
        message = "数字人服务启动失败，请联系系统管理员处理";
      } else if (e?.response?.data?.code == "10013") {
        message = "数字人服务停止失败，请联系系统管理员处理";
      }
      toast({
        title: "操作失败",
        description: `${
          message || e.message || "请检查网络或请联系系统管理员"
        }`,
        variant: "destructive",
      });
      if (params[0].status === "Running") {
        setInnerStatus("Stopped");
      } else {
        setInnerStatus("Running");
      }
    },
  });
  const handleEdit = () => {
    if (onEdit) {
      onEdit({
        id,
        audioClipId,
        name,
        videoClipId,
        knowledgeId,
        type,
      });
    }
  };

  const handleDelete = () => {
    if (onDelete) {
      onDelete(id);
    }
  };
  const runProject = () => {
    if (innerPublicUrl) {
      window.open(`/project/preview/${id}`);
    } else {
      toast({
        title: "操作失败",
        description: "项目已停止，请先启动项目",
        variant: "destructive",
      });
    }
  };

  const handleStatusToggle = (id: string, status: string) => {
    setUploading(true);
    if (status === "Stopped") {
      setInnerStatus("Running");
      runUpdate({ id, status: "Running" });
    } else {
      setInnerStatus("Stopped");
      runUpdate({ id, status: "Stopped" });
    }
  };
  const handleDesign = (e: React.MouseEvent) => {
    e.stopPropagation();
    window.open(`/project/design/${id}`);
  };

  return (
    <>
      <LoadingDialog
        open={uploading}
        message={`正在${
          innerStatus === "Running" ? "启动" : "停止"
        }项目，请稍候... `}
      />
      <TooltipProvider>
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: 10 }}
          whileHover={{ y: -5 }}
          transition={{ duration: 0.3 }}
        >
          <Card
            className={cn(
              "overflow-hidden transition-all duration-300 h-full flex flex-col",
              "hover:shadow-md",
              "border-opacity-40"
            )}
          >
            <div className="aspect-video relative overflow-hidden bg-muted">
              {type === "Image" ? (
                <img
                  src={videoClipUrl}
                  alt=""
                  className="w-full  object-cover"
                />
              ) : (
                <video src={videoClipUrl} className="w-full object-cover" />
              )}

              <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent flex items-end">
                <div className="w-full p-4">
                  <h3 className="font-semibold text-white text-lg truncate">
                    {name}
                  </h3>
                </div>
              </div>

              <div className="absolute top-2 right-2">
                <Badge
                  className={cn(
                    "bg-white text-black",
                    "flex items-center gap-1"
                  )}
                >
                  {type === "Video" ? (
                    <>
                      <VideoIcon className="h-3 w-3" />
                      视频形象
                    </>
                  ) : (
                    <>
                      <ImageIcon className="h-3 w-3" />
                      图片形象
                    </>
                  )}
                </Badge>
              </div>

              <Button
                size="icon"
                className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 rounded-full h-12 w-12 opacity-80 hover:opacity-100"
                onClick={runProject}
              >
                <Play className="h-5 w-5 ml-0.5" />
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="gap-1"
                onClick={handleDesign}
              >
                <PaintBucket className="h-4 w-4" />
                设计
              </Button>
            </div>

            <CardContent className="p-4 flex-grow">
              <div className="flex flex-wrap gap-2 mb-3">
                <Badge variant="outline" className="flex items-center gap-1">
                  <Music className="h-3 w-3" />
                  {audioClipName}
                </Badge>
              </div>
              <div className="flex flex-wrap gap-2 mb-3">
                <Badge variant="outline" className="flex items-center gap-1">
                  {knowledgeName.length > 10
                    ? knowledgeName.slice(0, 10) + "..."
                    : knowledgeName}
                </Badge>
              </div>

              <div className="text-sm text-muted-foreground">
                <p>
                  创建时间：
                  {createTime
                    ? dayjs(createTime).format("YYYY-MM-DD HH:mm:ss")
                    : ""}
                </p>
              </div>
            </CardContent>

            <CardFooter className="p-4 border-t bg-muted/20 flex justify-between">
              <div className="flex gap-2">
                <Button
                  variant={
                    innerStatus === "Running" ? "destructive" : "outline"
                  }
                  size="sm"
                  className="gap-1"
                  onClick={() => handleStatusToggle(id, innerStatus)}
                >
                  <Power className="h-4 w-4" />
                  {innerStatus === "Running" ? "停止" : "启动"}
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className="gap-1"
                  disabled={innerStatus == "Running"}
                  onClick={handleEdit}
                >
                  <Pencil className="h-4 w-4" />
                  编辑
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className="gap-1"
                  onClick={handleDesign}
                >
                  <PaintBucket className="h-4 w-4" />
                  设计
                </Button>
              </div>

              {/* <div className="flex items-center justify-center gap-2">
            <Switch
              checked={innerStatus === "Running"}
              onCheckedChange={() => handleStatusToggle(id, innerStatus)}
            />
            <span className="text-sm text-muted-foreground">
              {innerStatus === "Running" ? "已启用" : "已停用"}
            </span>
          </div> */}

              <DropdownMenu>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <DropdownMenuTrigger asChild>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-8 w-8"
                        onClick={(e) => e.stopPropagation()}
                      >
                        <MoreVertical className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                  </TooltipTrigger>
                  <TooltipContent>更多操作</TooltipContent>
                </Tooltip>
                <DropdownMenuContent align="end">
                  <DropdownMenuSeparator />
                  <DropdownMenuItem
                    disabled={innerStatus == "Running"}
                    onClick={handleDelete}
                    className="cursor-pointer text-destructive"
                  >
                    <Trash className="h-4 w-4 mr-2" />
                    删除
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </CardFooter>
          </Card>
        </motion.div>
      </TooltipProvider>
    </>
  );
};

export default ProjectCard;
