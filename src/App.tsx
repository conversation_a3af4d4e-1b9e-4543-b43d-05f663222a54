import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import { AuthProvider } from "./context/AuthContext";
import Login from "./pages/Login";
import NotFound from "./pages/NotFound";
import Resources from "./pages/Resources";
import Projects from "./pages/Projects";
import Knowledge from "./pages/Knowledge";
import DevelopmentGuide from "./pages/DevelopmentGuide";
import SystemManagement from "./pages/SystemManagement";
import UserManagement from "./pages/UserManagement";
import ResourceManagement from "./pages/ResourceManagement";
import Index from "./pages/Index";
import Preview from "./pages/Preview";
import ProjectDesign from "./pages/ProjectDesign";
import ProjectPreview from "./pages/ProjectPreview";
import ReferenceDemo from "./components/knowledge/ReferenceDemo";
import "./App.css";
import { time } from "console";

const App = () => {
  return (
    <BrowserRouter>
      <AuthProvider>
        <Routes>
          <Route path="/login" element={<Login />} />
          <Route path="/preview/:projectId" element={<Preview />} />
          <Route
            path="/project/design/:projectId"
            element={<ProjectDesign />}
          />
          <Route
            path="/project/preview/:projectId"
            element={<ProjectPreview />}
          />
          <Route path="/" element={<Index />} />
          <Route path="/resources" element={<Resources />} />
          <Route path="/projects" element={<Projects />} />
          <Route path="/knowledge" element={<Knowledge />} />
          <Route path="/reference-demo" element={<ReferenceDemo />} />
          <Route path="/development-guide" element={<DevelopmentGuide />} />

          {/* System Management Routes */}
          <Route path="/system-management" element={<SystemManagement />}>
            <Route path="users" element={<UserManagement />} />
            <Route path="resources" element={<ResourceManagement />} />
          </Route>

          {/* <Route path="*" element={<NotFound />} /> */}
        </Routes>
      </AuthProvider>
    </BrowserRouter>
  );
};

export default App;
