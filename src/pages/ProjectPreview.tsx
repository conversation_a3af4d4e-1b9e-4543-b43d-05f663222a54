import { useState, useEffect, useRef } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { useToast } from "@/components/ui/use-toast";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Slider } from "@/components/ui/slider";
import { AspectRatio } from "@/components/ui/aspect-ratio";
import PageTransition from "@/components/ui/PageTransition";
import MainLayout from "@/components/layout/MainLayout";
import LoadingDialog from "@/components/ui/LoadingDialog";
import {
  MicOff,
  ArrowLeft,
  Save,
  Image,
  Type,
  PaintBucket,
  Trash2,
  MoveHorizontal,
  MoveVertical,
  MessageSquare,
  Send,
} from "lucide-react";
import {
  cn,
  convertNumberFormat,
  createChromaKeyMaterial,
  generateRandomState,
  hexToRGBA,
  hexToVec3,
} from "@/lib/utils";
import axios from "axios";
import { RProject, type Project as ProjectFormat } from "@/request/project";
import store from "store2";
import { RDesign, type Design } from "@/request/design";
import { useRequest } from "ahooks";
import type { Response } from "../../vite-env";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import text1Img from "../assets/text1.png";
import voice1Img from "../assets/voice1.png";
import voiceActiveImg from "../assets/voice.png";
import textActiveImg from "../assets/text.png";
import voiceDisImg from "../assets/voiceDis.png";
import posterImg from "../assets/avata1.png";
import * as THREE from "three";
import { flushSync } from "react-dom";

interface Element {
  id: string;
  type: "text" | "video" | "button" | "subtitle" | "chat" | "message";
  x: number;
  y: number;
  width?: number;
  height?: number;
  content?: string;
  fontSize?: number;
  color?: string;
  fontWeight?: string;
  backgroundColor?: string;
  size?: number;
  iconScale?: number;
  // 字幕框特有属性
  padding?: number;
  borderRadius?: number;
  opacity?: number;
  fontFamily?: string;
  fontPadding?: number;
  btnOpacity?: number;
  aiBackgroundColor?: string;
  aiColor?: string;
  keyColor?: string | undefined;
  smoothing?: number;
  threshold?: number;
}

interface ProjectDesign {
  id: string;
  projectId: string;
  backgroundColor: string;
  backgroundImage: string | null;
  backgroundUrl: string | null;
  elements: Element[];
  aspectRatio: string;
  subtitles: boolean;
  scrollSubtitles: boolean;
  opacity?: number;
  version: number;
}

interface Project {
  id: string;
  title: string;
  avatar: {
    id: string;
    type: "video" | "image";
    thumbnail: string;
  };
}
interface HistoryItem {
  question: string | null;
  answer: string | null;
}

const defaultDesign: ProjectDesign = {
  id: "",
  projectId: "",
  backgroundColor: "#ffffff",
  backgroundImage: null,
  opacity: 1,
  elements: [
    // Default video element
    {
      id: "video-element",
      type: "video",
      x: 38,
      y: 7,
      width: 28,
      height: 0, // aspect ratio controlled
      keyColor: undefined,
      smoothing: 0.1,
      threshold: 0.35,
    },
    // Default mic button
    {
      id: "chat-button",
      type: "chat",
      x: 45,
      y: 84,
      size: 40,
      backgroundColor: "#cccccc",
      color: "#000000",
      iconScale: 0.5,
      btnOpacity: 1,
    },
    {
      id: "mic-button",
      type: "button",
      x: 53,
      y: 84,
      size: 40,
      backgroundColor: "#cccccc",
      color: "#000000",
      iconScale: 0.5,
      btnOpacity: 1,
    },
    {
      id: "subtitle-container",
      type: "subtitle",
      x: 39,
      y: 26,
      width: 25, // 百分比
      height: 50, // 百分比
      backgroundColor: "#357eb6",
      opacity: 0.5,
      padding: 16,
      borderRadius: 8,
      fontSize: 16,
      color: "#FFFFFF",
      fontFamily: "system-ui",
      fontWeight: "normal",
      fontPadding: 10,
      aiBackgroundColor: "#425057",
      aiColor: "#FFFFFF",
    },
    {
      id: "message-element",
      type: "message",
      x: 38,
      y: 91,
      width: 28,
      height: 8, // aspect ratio controlled
    },
  ],
  aspectRatio: "16:9",
  subtitles: true,
  version: 1,
};
let sessionid = null;

const ProjectDesign = () => {
  const { projectId } = useParams<{ projectId: string }>();
  const navigate = useNavigate();
  const { toast } = useToast();
  const [project, setProject] = useState<Project | null>(null);
  const [design, setDesign] = useState<ProjectDesign>({
    ...defaultDesign,
    projectId: projectId || "",
  });
  const [activeElementIndex, setActiveElementIndex] = useState<number | null>(
    null
  );
  const [isSaving, setIsSaving] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const designContainerRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [scale, setScale] = useState((window.innerHeight / 450).toFixed(2));
  const [isTouching, setIsTouching] = useState(false);

  //重构client
  const [useTurn] = useState(false);
  const [playBtn, setPlayBtn] = useState(false);
  const [fetchBaseUrl, setFetchBaseUrl] = useState("");
  const [knowledgeId, setKnowledgeId] = useState("");
  const [fetchWsUrl, setFetchWsUrl] = useState(import.meta.env.VITE_WS_URL);
  //const [fetchWsUrl, setFetchWsUrl] = useState("wss://tai.tslsmart.com:10096/");

  const [welcomeText] = useState("您好，请问有什么可以帮助您的吗");
  const [chartHistory, setChartHistory] = useState<HistoryItem[]>([]);

  const [videoRatio, setVideoRatio] = useState(null);

  var pc = null;
  var wsconnecter = null;
  let offline_text = "";
  let rec_text = "";
  //@ts-ignore
  var rec = Recorder({
    type: "pcm",
    bitRate: 16,
    sampleRate: 16000,
    onProcess: recProcess,
  });
  var sampleBuf = new Int16Array();
  const [isCanceled, setIsCanceled] = useState(false);
  const [isSpeaking, setIsSpeaking] = useState(false);
  const chartRef = useRef(null);
  //聊天逻辑
  const [isChatMode, setIsChatMode] = useState(false);
  const [inputMessage, setInputMessage] = useState("");
  const [keyColor, setKeyColor] = useState("#00ff00");

  const mountRef = useRef(null);

  const handleSendMessage = async () => {
    if (inputMessage.trim()) {
      // 处理发送消息的逻辑
      setChartHistory((prevChartHistory: any) => {
        return [
          ...prevChartHistory,
          { question: inputMessage.trim(), answer: "" },
        ];
      });
      const data = {
        query: inputMessage.trim(),
        //query: '回答字数100字以内，输出纯文本格式，不要markdown格式 不需要*号' + rec_text.trim(),
        knowledge_base_name: "yibao",
        top_k: 10,
        history_len: -1,
        history: [],
        stream: true,
        model_name: "Bit-LLM-Small",
        temperature: 0.5,
        max_tokens: 0,
        prompt_name: "default",
      };
      await postToLLM("/chat/knowledge_base_chat", data);
      setInputMessage("");
    }
  };

  const handleSendMessageRagflow = async () => {
    if (inputMessage.trim()) {
      sendToHuman("", true);
      // 处理发送消息的逻辑
      setChartHistory((prevChartHistory: any) => {
        return [
          ...prevChartHistory,
          { question: inputMessage.trim(), answer: "" },
        ];
      });
      const data = {
        knowledgeId: knowledgeId,
        question: inputMessage.trim() + "/no_think",
      };
      sentSentences = "";
      await postToLLMRagflow("/v1/chat", data);
      setInputMessage("");
    }
  };

  function recProcess(
    buffer,
    powerLevel,
    bufferDuration,
    bufferSampleRate,
    newBufferIdx,
    asyncEnd
  ) {
    if (wsconnecter) {
      var data_48k = buffer[buffer.length - 1];

      var array_48k = new Array(data_48k);
      //@ts-ignore
      var data_16k = Recorder.SampleData(
        array_48k,
        bufferSampleRate,
        16000
      ).data;

      sampleBuf = Int16Array.from([...sampleBuf, ...data_16k]);
      var chunk_size = 960; // for asr chunk_size [5, 10, 5]
      while (sampleBuf.length >= chunk_size) {
        let sendBuf = sampleBuf.slice(0, chunk_size);
        sampleBuf = sampleBuf.slice(chunk_size, sampleBuf.length);
        console.log("asr sendBuf:");
        wsconnecter.wsSend(sendBuf);
      }
    }
  }
  function start() {
    const config: any = {
      sdpSemantics: "unified-plan",
    };
    if (useTurn) {
      config.iceServers = [
        {
          urls: ["stun:stun.l.google.com:19302"],
        },
        {
          urls: ["turn:47.94.164.210:3478?transport=udp"],
          username: "tslapp",
          credential: "3A2a9ePPTnA",
        },
      ];
    }
    pc = new RTCPeerConnection(config);
    pc.addEventListener("track", (evt: any) => {
      if (evt.track.kind == "video") {
        const videoElement: any = document.getElementById("video");
        console.log("start video");
        videoElement.srcObject = evt.streams[0];
        const promise = videoElement.play();
        if (promise !== undefined) {
          promise
            .then((_) => {
              console.log("Autoplay started!");
              // Autoplay started!
            })
            .catch((error) => {
              console.error("Autoplay was prevented.", error);
              setPlayBtn(true);
              //playButton.style.display = "block";
              // Autoplay was prevented.
              // Show a "Play" button so that user can start playback.
            });
        }
      }
    });
    negotiate().then(() => {
      //document.getElementById('stop').style.display = 'inline-block';
      document.getElementById("canvas").style.display = "inline-block";

      setTimeout(() => {
        sendToHuman(welcomeText, false);
        setTimeout(() => {
          setChartHistory((prevChartHistory) => [
            ...prevChartHistory,
            { answer: welcomeText, question: null },
          ]);
        }, 1000);
      }, 2000);
    });
  }

  let sentSentences = ""; // 已发送句子，避免重复

  async function postToLLMRagflow(url: string, data: any) {
    const headers = {
      "Content-Type": "application/json",
      timeout: 60000,
    };

    const response = await fetch(url, {
      method: "POST",
      //@ts-ignore
      headers,
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const reader = response.body?.getReader();
    const decoder = new TextDecoder();

    if (!reader) {
      throw new Error("Response body is not readable");
    }

    while (true) {
      const { done, value } = await reader.read();
      if (done) break;

      const chunk = decoder.decode(value, { stream: true });
      const lines = chunk.split("\n");

      for (let line of lines) {
        if (!line.trim().startsWith("data:")) continue;

        try {
          const jsonStr = line.trim().substring(5); // remove 'data:'
          const parsed = JSON.parse(jsonStr);

          // 检查是否含有reference字段
          if (!parsed.data || !parsed.data.reference) continue;

          let answer = parsed.data.answer || "";

          //去掉markdown标记
          answer = answer
            .replace(/\*\*(.*?)\*\*/g, "$1")
            .replace(/\*(.*?)\*/g, "$1")
            .replace(/^#+\s*(.*)/gm, "$1")
            .replace(/`([^`]+)`/g, "$1")
            .replace(/\[([^\]]+)]\([^)]+\)/g, "$1")
            .replace(/\n{2,}/g, "\n")
            .trim();
          // 去掉 <think> 标记
          answer = answer.replace(/<think>[\s\S]*?<\/think>/g, "").trim();
          if (answer) {
            setChartHistory((prevChartHistory: any) => {
              return [
                ...prevChartHistory.slice(0, -1),
                {
                  answer: answer,
                  question:
                    prevChartHistory[prevChartHistory.length - 1].question,
                },
              ];
            });
          }
          let origin = answer;
          if (sentSentences.length > 0 && origin.includes(sentSentences)) {
            origin = origin.slice(
              origin.indexOf(sentSentences) + sentSentences.length
            );
          }

          // 正则匹配完整句子（以。！？结尾）
          const sentenceRegex = /([\s\S]*?[，：。！？])(?:\n|$)/;
          let match;

          if ((match = sentenceRegex.exec(origin)) !== null) {
            const sentence = match[1];
            console.log("match", match);
            //去除所有空白字符，包括空格、制表符、换行符等
            sendToHuman(sentence.replace(/[\s\uFEFF\xA0]+/g, ""), false);
            sentSentences += sentence;
          }
        } catch (err) {
          console.warn("JSON parse error or unexpected format:", line);
        }
      }
    }
  }

  async function postToLLM(url, data) {
    const headers = {
      "Content-Type": "application/json",
      timeout: 60000,
    };

    const response = await fetch(url, {
      method: "POST",
      //@ts-ignore
      headers: headers,
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let done = false;
    let resultBuffer = ""; // 缓存接收到的数据
    let sentenceBuffer = ""; // 用于临时缓存当前句子
    let isLast = false;

    while (!done) {
      const { value, done: readerDone } = await reader.read();
      done = readerDone;
      resultBuffer += decoder.decode(value, { stream: true }); // 将当前流解码并拼接

      while (true) {
        const jsonStartIndex = resultBuffer.indexOf("data: {");
        const jsonEndIndex = resultBuffer.indexOf("}", jsonStartIndex);

        if (jsonStartIndex !== -1 && jsonEndIndex !== -1) {
          const jsonString = resultBuffer.slice(
            jsonStartIndex + 6,
            jsonEndIndex + 1
          );
          try {
            const jsonData = JSON.parse(jsonString);
            const text = jsonData.answer; // 提取 'text' 字段的内容
            if (text) {
              isLast = true;
              sentenceBuffer += text; // 拼接文本到当前句子缓存
              console.log("=====text", text);
              setChartHistory((prevChartHistory: any) => {
                return [
                  ...prevChartHistory.slice(0, -1),
                  {
                    answer:
                      prevChartHistory[prevChartHistory.length - 1].answer +
                      text,
                    question:
                      prevChartHistory[prevChartHistory.length - 1].question,
                  },
                ];
              });

              const sentenceEndIndex = sentenceBuffer.search(/[。！？\n]/);
              if (sentenceEndIndex !== -1) {
                const completeSentence = sentenceBuffer
                  .slice(0, sentenceEndIndex + 1)
                  .trim();
                if (completeSentence) {
                  sendToHuman(convertNumberFormat(completeSentence), true);
                }
                sentenceBuffer = sentenceBuffer
                  .slice(sentenceEndIndex + 1)
                  .trim();
              }
            }
          } catch (error) {
            console.error("JSON 解析错误:", error);
          }
          resultBuffer = resultBuffer.slice(jsonEndIndex + 1);
        } else {
          break;
        }
      }
    }
    if (sentenceBuffer?.length) {
      sendToHuman(convertNumberFormat(sentenceBuffer));
    }

    reader.releaseLock();
  }

  function sendToHuman(message, interrupt = false) {
    console.log("sendToHuman:", message, interrupt, sessionid);
    //fetch('http://***********:8010/human', {
    fetch(`${fetchBaseUrl}/human`, {
      body: JSON.stringify({
        text: message,
        type: "echo",
        interrupt: interrupt,
        sessionid: parseInt(sessionid),
      }),
      headers: {
        "Content-Type": "application/json",
      },
      method: "POST",
    });
  }

  function negotiate() {
    pc.addTransceiver("video", { direction: "recvonly" });
    pc.addTransceiver("audio", { direction: "recvonly" });
    return pc
      .createOffer()
      .then((offer) => {
        return pc.setLocalDescription(offer);
      })
      .then(() => {
        // wait for ICE gathering to complete
        return new Promise((resolve) => {
          if (pc.iceGatheringState === "complete") {
            //@ts-ignore
            resolve();
          } else {
            const checkState = () => {
              if (pc.iceGatheringState === "complete") {
                pc.removeEventListener("icegatheringstatechange", checkState);
                //@ts-ignore
                resolve();
              }
            };
            pc.addEventListener("icegatheringstatechange", checkState);
          }
        });
      })
      .then(() => {
        var offer = pc.localDescription;
        //return fetch('http://***********:8010/offer', {
        return fetch(`${fetchBaseUrl}/offer`, {
          body: JSON.stringify({
            sdp: offer.sdp,
            type: offer.type,
          }),
          headers: {
            "Content-Type": "application/json",
          },
          method: "POST",
        });
      })
      .then((response) => {
        if (response?.status == 500) {
          return Promise.reject({ status: 500 });
        } else {
          return response.json();
        }
      })
      .then((answer) => {
        sessionid = answer.sessionid;
        return pc.setRemoteDescription(answer);
      })
      .catch((e) => {
        console.error("======fetch error", e);
        if (e?.status == 500) {
          alert("服务器最大并发数已达上限，请联系管理员处理");
        } else {
          const redirectUri = encodeURIComponent(window.location.href);
          const state = generateRandomState();
          window.location.href = `${fetchBaseUrl}/Auth.html?redirect_uri=${redirectUri}&state=${state}`;
        }
      });
  }
  function allowPlay() {
    const videoElement: any = document.getElementById("video");
    videoElement
      .play()
      .then(() => {
        setPlayBtn(false);
      })
      .catch((error) => {
        console.log("Playback failed:", error);
      });
  }

  function startRecording() {
    console.log("录音开始");

    if (wsconnecter) {
      wsconnecter?.wsStop();
      wsconnecter = null;
    }
    wsconnecter = new WebSocketConnectMethod({
      msgHandle: getJsonMessage,
      stateHandle: getConnState,
    });
    const ret = wsconnecter.wsStart(fetchWsUrl);
    rec.open(function () {
      if (wsconnecter) {
        rec.start();
      }
    });
  }

  function stopRecording() {
    console.log("录音结束");
    try {
      if (wsconnecter) {
        rec.stop(undefined, undefined, true);
        var chunk_size = new Array(5, 10, 5);
        var request = {
          chunk_size: chunk_size,
          wav_name: "h5",
          is_speaking: false,
          chunk_interval: 10,
          mode: "offline",
        };

        if (sampleBuf.length > 0) {
          wsconnecter.wsSend(sampleBuf);
          sampleBuf = new Int16Array();
        }
        wsconnecter.wsSend(JSON.stringify(request));
      }
    } catch (error) {
      console.log("err");
      console.log(error);
    }
  }

  function getJsonMessage(jsonMsg) {
    let rectxt = JSON.parse(jsonMsg.data)["text"];
    let asrmodel = JSON.parse(jsonMsg.data)["mode"];
    let is_final = JSON.parse(jsonMsg.data)["is_final"];

    if (asrmodel == "2pass-offline" || asrmodel == "offline") {
      offline_text = offline_text + rectxt;
      rec_text = offline_text;
    } else {
      rec_text = rec_text;
    }

    rec_text = rec_text.replace(/<\|.*?\|>/g, "");
    console.log("is final:", is_final);
    if (is_final == true) {
      wsconnecter.wsStop();
      sendToLLM();
    }
    console.log("asr rec_text:", rec_text);
  }
  function isStopCommand(input) {
    const stopWords = [
      "停止",
      "结束",
      "暂停",
      "退出",
      "停下",
      "停掉",
      "停止一下",
      "停",
      "关掉",
      "关闭",
      "终止",
      "别说了",
    ];

    // 将输入转换为小写，并去掉前后空格
    input = input.trim();

    // 判断输入的文本是否包含停止相关的词汇
    return stopWords.some((word) => input.includes(word));
  }
  async function sendToLLM() {
    if (!isCanceled) {
      if (isStopCommand(rec_text)) {
        sendToHuman("请问还有什么可以帮助您的？", true);
        setChartHistory((prevChartHistory) => [
          ...prevChartHistory,
          { answer: "请问还有什么可以帮助您的？", question: null },
        ]);
      } else if (rec_text && !isSpeaking && rec_text.length > 4) {
        console.log("chatLLM:", rec_text.trim());
        setChartHistory((prevChartHistory: any) => {
          return [...prevChartHistory, { question: rec_text, answer: "" }];
        });
        const data = {
          query: rec_text.trim(),
          //query: '回答字数100字以内，输出纯文本格式，不要markdown格式 不需要*号' + rec_text.trim(),
          knowledge_base_name: "yibao",
          top_k: 10,
          history_len: -1,
          history: [],
          stream: true,
          model_name: "Bit-LLM-Small",
          temperature: 0.5,
          max_tokens: 0,
          prompt_name: "default",
        };
        await postToLLM("/chat/knowledge_base_chat", data);
      }
    }
    console.log("清理rec_text");
    rec_text = "";
    offline_text = "";
  }
  function getConnState(connState) {
    if (connState === 0) {
    } else if (connState === 1) {
      //stop();
    } else if (connState === 2) {
      stop();
    }
  }
  function stop() {
    setTimeout(() => {
      pc.close();
    }, 500);

    document.getElementById("canvas").style.display = "none";
  }

  function recordVoice() {
    startRecording();
  }
  const { run: runDesign } = useRequest(RDesign, {
    manual: true,
    onSuccess: (data: Response<Design>) => {
      if (data?.data?.settings) {
        const result = data.data.settings;
        try {
          const designJson = JSON.parse(result);
          console.log("=====designJson", designJson);

          if (designJson.elements)
            flushSync(() => {
              setDesign({
                ...designJson,
                elements: [...designJson.elements],
                projectId,
                id: data.data.id,
                backgroundImage: data?.data?.backgroundUrl,
              });
            });

          runProject(projectId);
        } catch (e) {
          console.error("设计数据转化失败", e);
          runProject(projectId);
        }
      }
    },
    onError: (e) => {
      console.error("获取原始数据失败", e);
      runProject(projectId);
    },
  });

  const { run: runProject } = useRequest(RProject, {
    manual: true,
    onSuccess: (data: Response<ProjectFormat>) => {
      if (data.data.publicUrl) {
        setFetchBaseUrl(data.data.publicUrl);
        setKnowledgeId(data.data.knowledgeId);
      }
    },
    onError: (e) => {
      console.error("获取原始数据失败", e);
    },
  });

  useEffect(() => {
    function debounce(func, wait) {
      let timeout;
      return function () {
        clearTimeout(timeout);
        timeout = setTimeout(func, wait);
      };
    }
    const handleResize = debounce(function () {
      setScale((window.innerHeight / 450).toFixed(2));
      // 在这里添加你希望在窗口大小改变时执行的代码
    }, 250);

    window.addEventListener("resize", handleResize); // 等待250毫秒后执行函数

    return () => {
      window.removeEventListener("resize", handleResize);
      pc?.close();
      const videoElement = document.getElementById("video");
      if (videoElement) {
        cleanupVideoListeners(videoElement);
      }
    };
  }, []);

  useEffect(() => {
    const root = document.getElementById("root");
    //@ts-ignore
    root.style.padding = 0;
    //@ts-ignore
    root.style.margin = 0;
    const observer = new MutationObserver((mutations) => {
      chartRef.current.scrollTo({
        top: chartRef.current.scrollHeight,
        behavior: "smooth",
      });
    });

    observer.observe(chartRef.current, {
      childList: true,
      subtree: true,
      characterData: true,
    });

    return () => {
      observer.disconnect();
    };
  }, []);

  useEffect(() => {
    runDesign(projectId);
  }, []);
  useEffect(() => {
    if (fetchBaseUrl) {
      start();
      //确保视频元素已加载到 DOM 中
      const videoElement = document.getElementById("video");
      if (videoElement) {
        setupVideoListeners(videoElement, playBtn);
      }
    }
  }, [fetchBaseUrl]);

  // 设置视频事件监听器
  const setupVideoListeners = (videoElement, playBtn) => {
    // 尝试自动播放
    const attemptAutoPlay = () => {
      videoElement
        .play()
        .then(() => {
          console.log("视频自动播放成功");
          if (playBtn) {
            playBtn.style.display = "none"; // 隐藏按钮
          }
        })
        .catch((err) => {
          console.error("视频自动播放失败:", err);
          if (playBtn) {
            playBtn.style.display = "block"; // 显示按钮
          }
        });
    };

    videoElement.onloadedmetadata = () => {
      flushSync(() => {
        setVideoRatio(videoElement.videoWidth / videoElement.videoHeight);
      });

      const scene = new THREE.Scene();
      const camera = new THREE.PerspectiveCamera(
        90,
        videoElement.videoWidth / videoElement.videoHeight,
        0.1,
        1000
      );
      const zindex = Math.max(
        10 * (videoElement.videoWidth / videoElement.videoHeight),
        10
      );
      camera.position.z = zindex / 2;

      const renderer = new THREE.WebGLRenderer({
        antialias: true,
        alpha: true, // 开启透明度通道
        preserveDrawingBuffer: true, // 允许后续合成操作 });
      });
      console.log("mountRef.current.width", mountRef.current.clientWidth);
      renderer.setClearColor(0x000000, 0); // 设置透明清除色
      renderer.setSize(
        mountRef.current.clientWidth,
        (mountRef.current.clientWidth * videoElement.videoHeight) /
          videoElement.videoWidth
      );

      mountRef.current.appendChild(renderer.domElement);

      const videoTexture = new THREE.VideoTexture(videoElement);
      let material;
      if (design.elements[0].keyColor) {
        material = createChromaKeyMaterial(
          videoTexture,
          hexToVec3(design.elements[0].keyColor),
          design.elements[0].threshold,
          design.elements[0].smoothing
        );
      } else {
        material = createChromaKeyMaterial(
          videoTexture,
          hexToVec3("#ffffff"),
          0,
          0
        );
      }
      const geometry = new THREE.PlaneGeometry(
        10 * (videoElement.videoWidth / videoElement.videoHeight),
        10
      );

      const mesh = new THREE.Mesh(geometry, material);

      scene.add(mesh);

      const animate = () => {
        requestAnimationFrame(animate);
        renderer.render(scene, camera);
      };
      animate();
      videoElement.play();
    };
    // 监听视频播放事件
    videoElement.addEventListener("play", () => {
      if (playBtn) {
        playBtn.style.display = "none"; // 隐藏按钮
      }
    });

    // 监听视频错误事件
    videoElement.addEventListener("error", () => {
      if (playBtn) {
        playBtn.style.display = "block"; // 显示按钮
      }
    });

    // 初始尝试自动播放
    attemptAutoPlay();
  };

  // 清理视频事件监听器
  const cleanupVideoListeners = (videoElement) => {
    videoElement.removeEventListener("play", () => {});
    videoElement.removeEventListener("error", () => {});
    videoElement.onloadedmetadata = null;
  };

  const changeToVoiceMode = () => {
    setIsChatMode(false);
    setInputMessage("");
  };

  return (
    <div
      className="full-page-preview"
      style={{ width: "100vw", height: "100vh" }}
    >
      {playBtn && (
        <button
          onClick={allowPlay}
          style={{
            position: "fixed",
            top: "50%",
            left: "50%",
            transform: "translate(-50%,-50%)",
            padding: "10px 20px",
            fontSize: "16px",
            backgroundColor: "#4C82E6",
            color: "white",
            border: "none",
            borderRadius: "5px",
            cursor: "pointer",
            zIndex: 999,
          }}
        >
          由于浏览器安全策略限制，请点击此按钮允许数字人自动播放。
        </button>
      )}
      <div
        ref={designContainerRef}
        style={{
          height: "100vh",
          position: "relative",

          aspectRatio: design.aspectRatio.replace(":", "/"),
          backgroundColor: hexToRGBA(
            design.backgroundColor,
            design.opacity || 1
          ),
          backgroundImage: design.backgroundImage
            ? `url(${design.backgroundImage})`
            : "none",
          backgroundSize: "cover",
          backgroundPosition: "center",
        }}
      >
        {/* Elements */}
        {design.elements.map((element, index) => {
          // Render different element types
          if (element.type === "video") {
            return (
              <div
                key={element.id}
                className={cn("absolute cursor-move")}
                style={{
                  left: `${element.x}%`,
                  top: `${element.y}%`,
                  width: `${element.width}%`,
                }}
                // onClick={() => setActiveElementIndex(index)}
                // onMouseDown={(e) => handleElementDrag(e, index)}
              >
                <div
                  style={{ position: "relative" }}
                  className="overflow-hidden"
                >
                  <video
                    id="video"
                    autoPlay={true}
                    playsInline={true}
                    poster={posterImg}
                    style={{ position: "absolute", left: "-9999px" }}
                  ></video>

                  {videoRatio && (
                    <AspectRatio
                      ratio={videoRatio}
                      className="w-full"
                      ref={mountRef}
                    ></AspectRatio>
                  )}
                  <canvas
                    id="canvas"
                    style={{ width: "1px", height: "1px", display: "none" }}
                  ></canvas>
                  <audio id="audio" autoPlay={true}></audio>
                </div>
              </div>
            );
          } else if (element.type === "button") {
            return (
              <>
                {!isChatMode && (
                  <div
                    key={element.id}
                    className={cn("absolute cursor-move")}
                    style={{
                      left: `${element.x}%`,
                      top: `${element.y}%`,
                      transform: `scale(${scale})`,
                    }}
                  >
                    <div className="flex flex-col items-center">
                      <Button
                        className={cn(
                          "rounded-full flex items-center justify-center transition-all duration-300 cursor-pointer",
                          isTouching && "animate-pulse"
                        )}
                        style={{
                          backgroundColor: hexToRGBA(
                            element.backgroundColor,
                            0
                          ),
                          width: `${element.size}px`,
                          height: `${element.size}px`,
                          padding: 0,
                          // // 添加弥散光效果
                          // boxShadow: isTouching
                          //   ? `0 0 15px 5px ${element.backgroundColor}, 0 0 30px 10px ${element.backgroundColor}80, 0 0 45px 15px ${element.backgroundColor}40`
                          //   : "none",
                          // transition: "box-shadow 0.3s ease-in-out",
                        }}
                        onMouseDown={() => {
                          recordVoice();
                        }}
                        onMouseUp={() => {
                          stopRecording();
                          setIsTouching(false);
                        }}
                        onMouseDownCapture={() => {
                          setIsTouching(true);
                        }}
                      >
                        <img
                          src={isTouching ? voiceActiveImg : voice1Img}
                          style={{
                            width: `${element.size * element.iconScale}px`,
                            height: `${element.size * element.iconScale}px`,
                          }}
                          color={element.color}
                        />
                      </Button>
                    </div>
                  </div>
                )}
              </>
            );
          } else if (element.type === "text") {
            return (
              <div
                key={element.id}
                className={cn(
                  "absolute cursor-move select-none",
                  activeElementIndex === index
                    ? "ring-2 ring-primary ring-offset-2"
                    : ""
                )}
                style={{
                  left: `${element.x}%`,
                  top: `${element.y}%`,
                  transform: `scale(${scale})`,
                  fontSize: `${element.fontSize}px`,
                  color: element.color,
                  fontWeight: element.fontWeight,
                }}
                // onClick={() => setActiveElementIndex(index)}
                // onMouseDown={(e) => handleElementDrag(e, index)}
              >
                {element.content}
              </div>
            );
          } else if (element.type === "chat") {
            return (
              <>
                {!isChatMode && (
                  <div
                    key={element.id}
                    className={cn("absolute cursor-move")}
                    style={{
                      left: `${element.x}%`,
                      top: `${element.y}%`,
                      transform: `scale(${scale})`,
                    }}
                  >
                    <div className="flex flex-col items-center">
                      <Button
                        className="rounded-full flex items-center justify-center record-button"
                        style={{
                          backgroundColor: hexToRGBA(
                            element.backgroundColor,
                            0
                          ),
                          padding: 0,
                          width: `${element.size}px`,
                          height: `${element.size}px`,
                        }}
                        onClick={() => {
                          setIsChatMode(true);
                        }}
                      >
                        <img
                          src={text1Img}
                          style={{
                            width: `${element.size * element.iconScale}px`,
                            height: `${element.size * element.iconScale}px`,
                          }}
                          color={element.color}
                        />
                      </Button>
                    </div>
                  </div>
                )}
              </>
            );
          } else if (element.type === "subtitle" && design.subtitles) {
            return (
              <div
                key={element.id}
                id="chat-container"
                className={cn("absolute cursor-move")}
                style={{
                  left: `${element.x}%`,
                  top: `${element.y}%`,
                  width: `${element.width}%`,
                  height: `${element.height}%`,
                  borderRadius: `${element.borderRadius * Number(scale)}px`,

                  padding: `${element.padding * Number(scale)}px`,

                  fontSize: `${element.fontSize * Number(scale)}px`,
                  color: element.color,
                  fontFamily: element.fontFamily,
                  fontWeight: element.fontWeight,
                }}
              >
                <div
                  id="chat-messages"
                  ref={chartRef}
                  style={{
                    display: "flex",
                    flexDirection: "column",
                    gap: "10px",
                    maxHeight: "100%",
                    overflowY: "auto",
                    scrollPaddingBottom: "20px",
                  }}
                >
                  {chartHistory.map((item, index) => {
                    return (
                      <>
                        {item.question && (
                          <div
                            key={index + "question"}
                            style={{
                              backgroundColor: hexToRGBA(
                                element.backgroundColor,
                                element.opacity || 1
                              ),

                              padding: `${
                                element.fontPadding * Number(scale)
                              }px`,
                              fontSize: `${element.fontSize * Number(scale)}px`,
                              color: element.color,
                              width: "fit-content",
                              maxWidth: "90%",
                              alignSelf: "flex-end",
                              textAlign: "left",
                              borderRadius: `${element.borderRadius}px`,
                            }}
                          >
                            <ReactMarkdown remarkPlugins={[remarkGfm]}>
                              {item.question}
                            </ReactMarkdown>
                          </div>
                        )}
                        {item.answer && (
                          <div
                            key={index + "answer"}
                            style={{
                              backgroundColor: hexToRGBA(
                                element.aiBackgroundColor || "#425057",
                                element.opacity || 1
                              ),

                              padding: `${
                                element.fontPadding * Number(scale)
                              }px`,
                              fontSize: `${element.fontSize * Number(scale)}px`,
                              color: element.aiColor || "#FFFFFF",
                              width: "fit-content",
                              maxWidth: "90%",
                              alignSelf: "flex-start",
                              textAlign: "left",
                              borderRadius: `${element.borderRadius}px`,
                            }}
                          >
                            <ReactMarkdown remarkPlugins={[remarkGfm]}>
                              {item.answer}
                            </ReactMarkdown>
                          </div>
                        )}
                      </>
                    );
                  })}
                </div>
              </div>
            );
          } else if (element.type === "message" && isChatMode) {
            return (
              <div
                key={element.id}
                id="chat-container"
                className={cn("absolute cursor-move")}
                style={{
                  left: `${element.x}%`,
                  top: `${element.y}%`,
                  width: `${element.width}%`,
                  height: `${element.height}%`,
                }}
              >
                <div
                  className="flex px-1 items-center align-middle"
                  style={{
                    width: "100%",
                    height: "100%",
                    background: "rgba(0, 0, 0, 0.5)",
                    backdropFilter: "blur(10px)",
                  }}
                >
                  <button className="rounded-full bg-[rgba(255,255,255,0)] flex items-center justify-center py-1">
                    <MicOff
                      className="w-5 h-5 text-white"
                      onClick={changeToVoiceMode}
                      style={{
                        width: `${20 * Number(scale)}px`,
                        height: `${20 * Number(scale)}px`,
                      }}
                    />
                  </button>
                  <div
                    className="flex-1 flex items-center pr-1"
                    style={{ height: `scaleY(${Number(scale)})` }}
                  >
                    <input
                      type="text"
                      className={`bg-transparent py-2  pl-1 text-white placeholder-white/70 outline-none`}
                      placeholder="请输入"
                      value={inputMessage}
                      onChange={(e) => setInputMessage(e.target.value)}
                      onKeyDown={(e) => {
                        if (e.key === "Enter" && inputMessage.trim()) {
                          handleSendMessageRagflow();
                        }
                      }}
                      style={{
                        width: "100%",
                        fontSize: `${16 * Number(scale)}px`,
                        paddingRight: `${20 * Number(scale)}px`,
                      }}
                    />
                    <button
                      onClick={handleSendMessageRagflow}
                      className="rounded-full pr-1 flex items-center justify-center"
                      disabled={!inputMessage.trim()}
                      style={{
                        position: "absolute",
                        zIndex: 100,
                        right: 0,
                      }}
                    >
                      <img
                        src={textActiveImg}
                        className="w-6 h-6 text-white"
                        style={{
                          width: `${20 * Number(scale)}px`,
                          height: `${20 * Number(scale)}px`,
                        }}
                      />
                    </button>
                  </div>
                </div>
              </div>
            );
          }
          return null;
        })}
      </div>
    </div>
  );
};

export default ProjectDesign;
