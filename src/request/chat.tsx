import axios from "./index";

// 创建聊天会话接口
export const createChatSession = async (knowledgeId: string): Promise<string> => {
  const response = await axios.post("/v1/session", {
    knowledgeId: knowledgeId
  });
  return response.data;
};

// 聊天接口参数类型
export interface ChatRequest {
  knowledgeId: string;
  question: string;
  sessionId: string;
}

// 参考文档块类型
export interface ReferenceChunk {
  document_name: string;
  content: string;
  score?: number;
  metadata?: Record<string, any>;
}

// 参考文档类型
export interface Reference {
  chunks?: ReferenceChunk[];
  [key: string]: any;
}

// 聊天响应数据类型
export interface ChatResponse {
  code: number;
  message: string;
  data: {
    answer: string;
    reference: Reference;
    param: Array<{
      key: string;
      name: string;
      optional: boolean;
      type: string;
    }>;
    id: string;
    session_id: string;
  };
}

// 聊天接口 - 使用SSE流式接收
export const sendChatMessage = async (
  params: ChatRequest,
  onMessage: (data: ChatResponse) => void,
  onError: (error: Error) => void,
  onComplete: () => void
): Promise<void> => {
  try {
    const response = await fetch("/v1/chat", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(params),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    // 检查是否是流式响应
    const contentType = response.headers.get("content-type");

    if (contentType?.includes("application/json")) {
      // 如果是普通JSON响应，直接解析
      const data = await response.json();
      onMessage(data);
      onComplete();
      return;
    }

    // 处理流式响应
    const reader = response.body?.getReader();
    const decoder = new TextDecoder();

    if (!reader) {
      throw new Error("Response body is not readable");
    }

    let buffer = "";

    while (true) {
      const { done, value } = await reader.read();

      if (done) {
        // 处理缓冲区中剩余的数据
        if (buffer.trim()) {
          try {
            const line = buffer.trim();
            if (line.startsWith('data:')) {
              const jsonStr = line.substring(5);
              const data = JSON.parse(jsonStr);
              onMessage(data);
            } else if (line.startsWith('data: ')) {
              const jsonStr = line.substring(6);
              const data = JSON.parse(jsonStr);
              onMessage(data);
            } else {
              const data = JSON.parse(line);
              onMessage(data);
            }
          } catch (parseError) {
            console.warn("无法解析最终数据:", buffer);
          }
        }
        onComplete();
        break;
      }

      const chunk = decoder.decode(value, { stream: true });
      buffer += chunk;

      // 尝试解析完整的JSON对象
      const lines = buffer.split('\n');

      for (let i = 0; i < lines.length - 1; i++) {
        const line = lines[i].trim();
        if (line === '') continue;

        try {
          // 检查是否是SSE格式
          if (line.startsWith('data:')) {
            // SSE格式：data:{"json":"data"}
            const jsonStr = line.substring(5); // 移除 "data:" 前缀
            const data = JSON.parse(jsonStr);
            onMessage(data);
          } else if (line.startsWith('data: ')) {
            // SSE格式：data: {"json":"data"}
            const jsonStr = line.substring(6); // 移除 "data: " 前缀
            const data = JSON.parse(jsonStr);
            onMessage(data);
          } else {
            // 尝试直接解析JSON数据
            const data = JSON.parse(line);
            onMessage(data);
          }
        } catch (parseError) {
          console.warn('无法解析的数据行:', line);
        }
      }

      // 保留最后一行（可能不完整）
      buffer = lines[lines.length - 1];
    }
  } catch (error) {
    onError(error as Error);
  }
};
